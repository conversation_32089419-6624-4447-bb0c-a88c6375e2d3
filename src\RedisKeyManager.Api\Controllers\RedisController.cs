using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using RedisKeyManager.Api.Models;
using RedisKeyManager.Api.Services;

namespace RedisKeyManager.Api.Controllers
{
    /// <summary>
    /// Provides operations for managing Redis keys and values.
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class RedisController : ControllerBase
    {
        private readonly IRedisService _redisService;
        private readonly ILogger<RedisController> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="RedisController"/> class.
        /// </summary>
        /// <param name="redisService">The Redis service.</param>
        /// <param name="logger">The logger.</param>
        public RedisController(IRedisService redisService, ILogger<RedisController> logger)
        {
            _redisService = redisService;
            _logger = logger;
        }

        /// <summary>
        /// Searches for keys matching the specified pattern.
        /// </summary>
        /// <param name="pattern">The pattern to search for.</param>
        /// <param name="cursor">The cursor from which to start the scan.</param>
        /// <param name="pageSize">The maximum number of keys to return.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>A paged list of matching keys.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/redis/keys?pattern=user:*
        ///
        /// Sample response:
        ///
        ///     {
        ///       "keys": ["user:1", "user:2"],
        ///       "nextCursor": 0
        ///     }
        /// </remarks>
        /// <response code="200">Returns the matching keys.</response>
        /// <response code="500">Server error retrieving keys.</response>
        [HttpGet("keys")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(KeySearchResult))]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> SearchKeys([FromQuery] string pattern, [FromQuery] long cursor = 0, [FromQuery] int pageSize = 10, CancellationToken cancellationToken = default)
        {
            try
            {
                var result = await _redisService.SearchKeysAsync(pattern, cursor, pageSize, cancellationToken);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching keys with pattern {Pattern}", pattern);
                throw;
            }
        }

        /// <summary>
        /// Gets the value associated with the specified key.
        /// </summary>
        /// <param name="key">The Redis key.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>The value of the key.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/redis/value/myKey
        ///
        /// Sample response:
        ///
        ///     "my-value"
        /// </remarks>
        /// <response code="200">Returns the value for the key.</response>
        /// <response code="400">Key is required.</response>
        /// <response code="404">Key was not found.</response>
        /// <response code="500">Server error retrieving value.</response>
        [HttpGet("value/{key}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(string))]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(string))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> GetValue(string key, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return BadRequest("Key is required.");
            }

            try
            {
                var value = await _redisService.GetValueAsync(key, cancellationToken);
                if (value is null)
                {
                    return NotFound();
                }

                return Ok(value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving value for key {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// Updates the value for the specified key.
        /// </summary>
        /// <param name="key">The Redis key.</param>
        /// <param name="request">The request containing the new value.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>No content on success.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT /api/redis/value/myKey
        ///     {
        ///       "value": "new-value"
        ///     }
        /// </remarks>
        /// <response code="204">Value updated successfully.</response>
        /// <response code="400">Key or value is missing.</response>
        /// <response code="404">Key does not exist.</response>
        /// <response code="500">Failed to set value.</response>
        [HttpPut("value/{key}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(string))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> UpdateValue(string key, [FromBody] UpdateValueRequest request, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return BadRequest("Key is required.");
            }

            if (request?.Value is null)
            {
                return BadRequest("Value is required.");
            }

            try
            {
                var existing = await _redisService.GetValueAsync(key, cancellationToken);
                if (existing is null)
                {
                    return NotFound();
                }

                var success = await _redisService.SetValueAsync(key, request.Value, request.Expiry, cancellationToken);
                if (!success)
                {
                    _logger.LogError("Failed to set value for key {Key}", key);
                    return Problem("Failed to set value for key.", statusCode: 500);
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating value for key {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// Deletes the specified key from Redis.
        /// </summary>
        /// <param name="key">The Redis key.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>No content if the key was deleted.</returns>
        /// <remarks>
        /// Sample request:
        ///
        ///     DELETE /api/redis/value/myKey
        ///
        /// Sample response:
        ///     204 No Content
        /// </remarks>
        /// <response code="204">Key deleted.</response>
        /// <response code="400">Key is required.</response>
        /// <response code="404">Key was not found.</response>
        /// <response code="500">Server error deleting key.</response>
        [HttpDelete("value/{key}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(string))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> DeleteKey(string key, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return BadRequest("Key is required.");
            }

            try
            {
                var deleted = await _redisService.DeleteKeyAsync(key, cancellationToken);
                if (!deleted)
                {
                    return NotFound();
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting key {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// Creates a new key with the specified value.
        /// </summary>
        /// <param name="key">The Redis key.</param>
        /// <param name="request">The request containing the value.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>Created on success or conflict if the key exists.</returns>
        /// <response code="201">Key created.</response>
        /// <response code="400">Key or value is missing.</response>
        /// <response code="409">Key already exists.</response>
        /// <response code="500">Failed to set value.</response>
        [HttpPost("value/{key}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(string))]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> CreateValue(string key, [FromBody] CreateValueRequest request, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return BadRequest("Key is required.");
            }

            if (request?.Value is null)
            {
                return BadRequest("Value is required.");
            }

            try
            {
                var created = await _redisService.SetValueIfNotExistsAsync(key, request.Value, request.Expiry, cancellationToken);
                if (!created)
                {
                    return Conflict();
                }

                return CreatedAtAction(nameof(GetValue), new { key }, request.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating value for key {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// Retrieves metadata for the specified key.
        /// </summary>
        /// <param name="key">The Redis key.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>The key metadata.</returns>
        /// <response code="200">Returns the metadata.</response>
        /// <response code="404">Key was not found.</response>
        /// <response code="500">Server error retrieving metadata.</response>
        [HttpGet("metadata/{key}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(KeyMetadata))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> GetMetadata(string key, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return BadRequest("Key is required.");
            }

            try
            {
                var metadata = await _redisService.GetMetadataAsync(key, cancellationToken);
                if (metadata is null)
                {
                    return NotFound();
                }

                return Ok(metadata);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving metadata for key {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// Retrieves all fields and values of a hash key.
        /// </summary>
        /// <param name="key">The Redis hash key.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>The hash fields and values.</returns>
        /// <response code="200">Returns the hash as a dictionary.</response>
        /// <response code="404">Key was not found or is not a hash.</response>
        /// <response code="500">Server error retrieving hash.</response>
        [HttpGet("hash/{key}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(Dictionary<string, string>))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> GetHash(string key, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key))
            {
                return BadRequest("Key is required.");
            }

            try
            {
                var hash = await _redisService.GetHashAsync(key, cancellationToken);
                if (hash is null)
                {
                    return NotFound();
                }

                return Ok(hash);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving hash for key {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// Retrieves the value of a specific field in a hash.
        /// </summary>
        /// <param name="key">The Redis hash key.</param>
        /// <param name="field">The field within the hash.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>The field value.</returns>
        /// <response code="200">Returns the field value.</response>
        /// <response code="404">Key or field was not found.</response>
        /// <response code="500">Server error retrieving hash field.</response>
        [HttpGet("hash/{key}/{field}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(string))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> GetHashField(string key, string field, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(field))
            {
                return BadRequest("Key and field are required.");
            }

            try
            {
                var value = await _redisService.GetHashFieldAsync(key, field, cancellationToken);
                if (value is null)
                {
                    return NotFound();
                }

                return Ok(value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving hash field {Field} for key {Key}", field, key);
                throw;
            }
        }

        /// <summary>
        /// Updates the value of a hash field.
        /// </summary>
        /// <param name="key">The Redis hash key.</param>
        /// <param name="field">The field to update.</param>
        /// <param name="request">The request containing the new value.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>No content on success.</returns>
        /// <response code="204">Field updated successfully.</response>
        /// <response code="400">Key, field, or value is missing.</response>
        /// <response code="404">Key or field does not exist.</response>
        /// <response code="500">Failed to update field.</response>
        [HttpPut("hash/{key}/{field}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(string))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> UpdateHashField(string key, string field, [FromBody] HashFieldRequest request, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(field))
            {
                return BadRequest("Key and field are required.");
            }

            if (request?.Value is null)
            {
                return BadRequest("Value is required.");
            }

            try
            {
                var existing = await _redisService.GetHashFieldAsync(key, field, cancellationToken);
                if (existing is null)
                {
                    return NotFound();
                }

                await _redisService.SetHashFieldAsync(key, field, request.Value, cancellationToken);
                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating hash field {Field} for key {Key}", field, key);
                throw;
            }
        }

        /// <summary>
        /// Creates a new field in a hash.
        /// </summary>
        /// <param name="key">The Redis hash key.</param>
        /// <param name="field">The field to create.</param>
        /// <param name="request">The request containing the value.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>Created on success or conflict if the field exists.</returns>
        /// <response code="201">Field created.</response>
        /// <response code="400">Key, field, or value is missing.</response>
        /// <response code="409">Field already exists.</response>
        /// <response code="500">Failed to set field value.</response>
        [HttpPost("hash/{key}/{field}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(string))]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> CreateHashField(string key, string field, [FromBody] HashFieldRequest request, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(field))
            {
                return BadRequest("Key and field are required.");
            }

            if (request?.Value is null)
            {
                return BadRequest("Value is required.");
            }

            try
            {
                var created = await _redisService.SetHashFieldIfNotExistsAsync(key, field, request.Value, cancellationToken);
                if (!created)
                {
                    return Conflict();
                }

                return CreatedAtAction(nameof(GetHashField), new { key, field }, request.Value);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating hash field {Field} for key {Key}", field, key);
                throw;
            }
        }

        /// <summary>
        /// Deletes a field from a hash key.
        /// </summary>
        /// <param name="key">The Redis hash key.</param>
        /// <param name="field">The field to delete.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>No content if the field was deleted.</returns>
        /// <response code="204">Field deleted.</response>
        /// <response code="400">Key or field is required.</response>
        /// <response code="404">Field was not found.</response>
        /// <response code="500">Server error deleting field.</response>
        [HttpDelete("hash/{key}/{field}")]
        [Produces("application/json")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest, Type = typeof(string))]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError, Type = typeof(ProblemDetails))]
        public async Task<IActionResult> DeleteHashField(string key, string field, CancellationToken cancellationToken = default)
        {
            if (string.IsNullOrWhiteSpace(key) || string.IsNullOrWhiteSpace(field))
            {
                return BadRequest("Key and field are required.");
            }

            try
            {
                var deleted = await _redisService.DeleteHashFieldAsync(key, field, cancellationToken);
                if (!deleted)
                {
                    return NotFound();
                }

                return NoContent();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting hash field {Field} for key {Key}", field, key);
                throw;
            }
        }
    }
}
