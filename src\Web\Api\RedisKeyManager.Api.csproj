﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
		<UserSecretsId>ef818a9d-a691-4993-b3e3-782c7cd390cf</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<DockerfileContext>..\..\..</DockerfileContext>
		<PackageReadmeFile>README.md</PackageReadmeFile>
	</PropertyGroup>

	<ItemGroup>
		<Folder Include="Controllers\v2\" />
	</ItemGroup>

	<ItemGroup>

		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.7">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>

		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />

		<None Include="README.md" Pack="true" PackagePath="\" />

	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\Core\Application\RedisKeyManager.Application.csproj" />
		<ProjectReference Include="..\ApiFramework\RedisKeyManager.ApiFramework.csproj" />
	</ItemGroup>

</Project>
