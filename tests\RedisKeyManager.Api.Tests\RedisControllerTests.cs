using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using RedisKeyManager.Api.Controllers;
using RedisKeyManager.Api.Models;
using RedisKeyManager.Api.Services;
using Xunit;

namespace RedisKeyManager.Api.Tests;

public class RedisControllerTests
{
    private static RedisController CreateController(Mock<IRedisService> serviceMock)
    {
        var logger = Mock.Of<ILogger<RedisController>>();
        var controller = new RedisController(serviceMock.Object, logger);
        controller.ControllerContext = new ControllerContext
        {
            HttpContext = new DefaultHttpContext()
        };
        return controller;
    }

    [Fact]
    public async Task SearchKeys_ReturnsOk()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.SearchKeysAsync("pattern", 0, 10, It.IsAny<CancellationToken>()))
            .ReturnsAsync(new KeySearchResult { Keys = new[] { "a" }, NextCursor = 0 });

        var controller = CreateController(serviceMock);

        var result = await controller.SearchKeys("pattern", 0, 10);

        Assert.IsType<OkObjectResult>(result);
    }

    [Fact]
    public async Task GetValue_ReturnsNotFound_WhenMissing()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.GetValueAsync("key", It.IsAny<CancellationToken>()))
            .ReturnsAsync((string?)null);

        var controller = CreateController(serviceMock);

        var result = await controller.GetValue("key");

        Assert.IsType<NotFoundResult>(result);
    }

    [Fact]
    public async Task GetValue_ReturnsOk_WhenExists()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.GetValueAsync("key", It.IsAny<CancellationToken>()))
            .ReturnsAsync("value");

        var controller = CreateController(serviceMock);

        var result = await controller.GetValue("key");

        var ok = Assert.IsType<OkObjectResult>(result);
        Assert.Equal("value", ok.Value);
    }

    [Fact]
    public async Task UpdateValue_ReturnsNoContent_OnSuccess()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.GetValueAsync("key", It.IsAny<CancellationToken>()))
            .ReturnsAsync("old");
        serviceMock.Setup(s => s.SetValueAsync("key", "new", TimeSpan.FromMinutes(1), It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var controller = CreateController(serviceMock);

        var result = await controller.UpdateValue("key", new UpdateValueRequest { Value = "new", Expiry = TimeSpan.FromMinutes(1) });

        Assert.IsType<NoContentResult>(result);
    }

    [Fact]
    public async Task DeleteKey_ReturnsNoContent_OnSuccess()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.DeleteKeyAsync("key", It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var controller = CreateController(serviceMock);

        var result = await controller.DeleteKey("key");

        Assert.IsType<NoContentResult>(result);
    }

    [Fact]
    public async Task CreateValue_ReturnsCreated_OnSuccess()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.SetValueIfNotExistsAsync("key", "value", null, It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var controller = CreateController(serviceMock);

        var result = await controller.CreateValue("key", new CreateValueRequest { Value = "value" });

        Assert.IsType<CreatedAtActionResult>(result);
    }

    [Fact]
    public async Task CreateValue_ReturnsConflict_WhenExists()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.SetValueIfNotExistsAsync("key", "value", null, It.IsAny<CancellationToken>()))
            .ReturnsAsync(false);

        var controller = CreateController(serviceMock);

        var result = await controller.CreateValue("key", new CreateValueRequest { Value = "value" });

        Assert.IsType<ConflictResult>(result);
    }

    [Fact]
    public async Task GetMetadata_ReturnsOk_OnSuccess()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.GetMetadataAsync("key", It.IsAny<CancellationToken>()))
            .ReturnsAsync(new KeyMetadata { Ttl = TimeSpan.FromSeconds(10), Type = "string" });

        var controller = CreateController(serviceMock);

        var result = await controller.GetMetadata("key");

        Assert.IsType<OkObjectResult>(result);
    }

    [Fact]
    public async Task GetMetadata_ReturnsNotFound_WhenMissing()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.GetMetadataAsync("key", It.IsAny<CancellationToken>()))
            .ReturnsAsync((KeyMetadata?)null);

        var controller = CreateController(serviceMock);

        var result = await controller.GetMetadata("key");

        Assert.IsType<NotFoundResult>(result);
    }

    [Fact]
    public async Task GetHashField_ReturnsOk_WhenExists()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.GetHashFieldAsync("key", "field", It.IsAny<CancellationToken>()))
            .ReturnsAsync("value");

        var controller = CreateController(serviceMock);

        var result = await controller.GetHashField("key", "field");

        var ok = Assert.IsType<OkObjectResult>(result);
        Assert.Equal("value", ok.Value);
    }

    [Fact]
    public async Task GetHashField_ReturnsNotFound_WhenMissing()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.GetHashFieldAsync("key", "field", It.IsAny<CancellationToken>()))
            .ReturnsAsync((string?)null);

        var controller = CreateController(serviceMock);

        var result = await controller.GetHashField("key", "field");

        Assert.IsType<NotFoundResult>(result);
    }

    [Fact]
    public async Task UpdateHashField_ReturnsNoContent_OnSuccess()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.GetHashFieldAsync("key", "field", It.IsAny<CancellationToken>()))
            .ReturnsAsync("old");

        var controller = CreateController(serviceMock);

        var result = await controller.UpdateHashField("key", "field", new HashFieldRequest { Value = "new" });

        Assert.IsType<NoContentResult>(result);
    }

    [Fact]
    public async Task CreateHashField_ReturnsCreated_OnSuccess()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.SetHashFieldIfNotExistsAsync("key", "field", "value", It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var controller = CreateController(serviceMock);

        var result = await controller.CreateHashField("key", "field", new HashFieldRequest { Value = "value" });

        Assert.IsType<CreatedAtActionResult>(result);
    }

    [Fact]
    public async Task DeleteHashField_ReturnsNoContent_OnSuccess()
    {
        var serviceMock = new Mock<IRedisService>();
        serviceMock.Setup(s => s.DeleteHashFieldAsync("key", "field", It.IsAny<CancellationToken>()))
            .ReturnsAsync(true);

        var controller = CreateController(serviceMock);

        var result = await controller.DeleteHashField("key", "field");

        Assert.IsType<NoContentResult>(result);
    }
}
