using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using RedisKeyManager.ApiFramework.Tools;
using RedisKeyManager.Application.Audit.Models;
using RedisKeyManager.Application.Audit.Queries;
using RedisKeyManager.Common.General;
using RedisKeyManager.Common.Utilities;
using RedisKeyManager.Domain.Entities;
using RedisKeyManager.Api.Controllers.v1;
using Swashbuckle.AspNetCore.Annotations;
using Swashbuckle.AspNetCore.Filters;
using RedisKeyManager.Application.Services;

namespace RedisKeyManager.Api.Controllers.v1.Audit
{
    /// <summary>
    /// Provides access to general audit logs.
    /// </summary>
    [ApiController]
    [ApiVersion("1")]
    [Authorize(Policy = AuthorizationPolicies.AuditAccess)]
    public class AuditController : BaseControllerV1
    {
        private readonly IRedisAuditService _auditService;

        public AuditController(IRedisAuditService auditService)
        {
            _auditService = auditService;
        }

        /// <summary>
        /// Retrieve paged audit logs with optional start and end timestamps.
        /// </summary>
        /// <param name="start">Inclusive UTC start timestamp for log retrieval.</param>
        /// <param name="end">Optional UTC end timestamp to limit results.</param>
        /// <param name="page">Page number (1-based).</param>
        /// <param name="pageSize">Number of logs per page.</param>
        /// <param name="ct">Cancellation token.</param>
        [HttpGet]
        [SwaggerOperation(
            Summary = "Retrieve audit logs",
            Description = "Retrieves paged audit log entries filtered by start date and optionally by end date.")]
        [ProducesResponseType(typeof(PagedApiResult<IList<AuditLogDto>>), StatusCodes.Status200OK)]
        [ProducesResponseType(typeof(ApiResult), StatusCodes.Status400BadRequest)]
        [ProducesResponseType(typeof(ApiResult), StatusCodes.Status500InternalServerError)]
        [SwaggerResponse(StatusCodes.Status200OK, "Paged audit logs", typeof(PagedApiResult<IList<AuditLogDto>>))]
        [SwaggerResponse(StatusCodes.Status400BadRequest, "Invalid filter parameters", typeof(ApiResult))]
        [SwaggerResponse(StatusCodes.Status500InternalServerError, "Internal server error", typeof(ApiResult))]
        [SwaggerResponseExample(StatusCodes.Status200OK, typeof(AuditLogsResponseExample))]
        [SwaggerResponseExample(StatusCodes.Status400BadRequest, typeof(AuditLogsErrorResponseExample))]
        [SwaggerResponseExample(StatusCodes.Status500InternalServerError, typeof(AuditLogsServerErrorResponseExample))]
        public async Task<IActionResult> GetLogs(
            [FromQuery] DateTime start,
            [FromQuery] DateTime? end,
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 10,
            CancellationToken ct = default)
        {
            var query = new GetAuditLogsQuery
            {
                Start = start,
                End = end,
                Page = page,
                PageSize = pageSize
            };

            var result = await _mediator.Send(query, ct);

            return new PagedApiResult<IList<AuditLogDto>>(result.Results, result.RowCount);
        }

        [HttpGet("redis/from/{from}")]
        public async Task<IActionResult> GetRedisAuditLogs(DateTime from, [FromQuery] DateTime? to = null, [FromQuery] int page = 1, [FromQuery] int pageSize = 10, CancellationToken cancellationToken = default)
        {
            var logs = await _auditService.GetAuditLogsAsync(from, to, page, pageSize, cancellationToken);
            return Ok(logs);
        }
    }

    /// <summary>
    /// Example response for audit logs.
    /// </summary>
    public class AuditLogsResponseExample : IExamplesProvider<PagedApiResult<IList<AuditLogDto>>>
    {
        public PagedApiResult<IList<AuditLogDto>> GetExamples()
        {
            var logs = new List<AuditLogDto>
            {
                new AuditLogDto
                {
                    UserId = 1,
                    ActionType = AuditActionType.CREATE,
                    Key = "sample:key",
                    TimestampUtc = DateTime.UtcNow,
                    OldValue = null,
                    NewValue = SecurityHelper.GetSha256Hash("value"),
                    IpAddress = "127.0.0.1",
                    UserAgent = "Swagger",
                    SessionId = "session-123"
                }
            };

            return new PagedApiResult<IList<AuditLogDto>>(logs, logs.Count);
        }
    }

    /// <summary>
    /// Example error response for invalid filter parameters.
    /// </summary>
    public class AuditLogsErrorResponseExample : IExamplesProvider<ApiResult>
    {
        public ApiResult GetExamples()
        {
            return new ApiResult(null, StatusCodes.Status400BadRequest, new[] { "Start must be earlier than End." });
        }
    }

    /// <summary>
    /// Example error response for server failures.
    /// </summary>
    public class AuditLogsServerErrorResponseExample : IExamplesProvider<ApiResult>
    {
        public ApiResult GetExamples()
        {
            return new ApiResult(null, StatusCodes.Status500InternalServerError, new[] { "An unexpected error occurred." });
        }
    }
}

