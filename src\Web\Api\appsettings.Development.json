{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AppOptions": {"WriteDatabaseConnectionString": "Server=(localdb)\\mssqllocaldb;Database=RedisKeyManagerDb;Trusted_Connection=True;MultipleActiveResultSets=true", "ReadDatabaseConnectionString": "Server=(localdb)\\mssqllocaldb;Database=RedisKeyManagerDb;Trusted_Connection=True;MultipleActiveResultSets=true", "RedisConnectionString": "localhost:6379", "AuthenticationServerUri": "", "ActivateSwagger": true, "CorsEnableUris": []}, "SiteSettings": {"JwtSettings": {"SecretKey": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "EncryptKey": "16CharEncryptKey", "Issuer": "CleanArchTemplate", "Audience": "CleanArchTemplate", "NotBeforeMinutes": "0", "ExpirationMinutes": "1440", "RefreshTokenValidityInDays": 7}, "IdentitySettings": {"PasswordRequireDigit": "true", "PasswordRequiredLength": "6", "PasswordRequireNonAlphanumeric": "false", "PasswordRequireUppercase": "false", "PasswordRequireLowercase": "false", "RequireUniqueEmail": "true"}}, "CacheConfig": {"DefaultCacheTime": 60, "ShortTermCacheTime": 3, "BundledFilesCacheTime": 120}, "DistributedCacheConfig": {"DistributedCacheType": "redis", "Enabled": true, "ConnectionString": "127.0.0.1:6379,ssl=False", "SchemaName": "dbo", "TableName": "DistributedCache"}, "Serilog": {"WriteTo": [{"Name": "Elasticsearch", "Args": {"nodeUris": "localhost:9200", "indexFormat": "cleanArch-log", "restrictedToMinimumLevel": "Warning", "autoRegisterTemplate": true, "autoRegisterTemplateVersion": "ESv6"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"restrictedToMinimumLevel": "Information"}}]}, "HealthChecksUI": {"HealthChecks": [{"Name": "Clean Template Project Service", "Uri": "https://localhost:44398/health"}], "EvaluationTimeInSeconds": 5}}