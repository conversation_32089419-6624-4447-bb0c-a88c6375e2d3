# =========================
# Build / Artifacts
# =========================
[Bb]in/
[Oo]bj/
[Dd]ebug/
[Dd]ebugPublic/
[Rr]elease/
[Rr]eleases/
[Bb]ld/
[Ll]og/
[Ll]ogs/
x64/
x86/
[Ww][Ii][Nn]32/
[Aa][Rr][Mm]/
[Aa][Rr][Mm]64/
artifacts/

# =========================
# IDEs & Tooling
# =========================
.vs/
# VS Code
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace
# JetBrains
.idea/
*.sln.iml
# Rider extra
.run/

# =========================
# Test / Coverage
# =========================
[Tt]est[Rr]esult*/
[Bb]uild[Ll]og.*
*.coverage
*.coveragexml
coverage/
coverage*.json
coverage*.xml
coverage*.info
BenchmarkDotNet.Artifacts/
nunit-*.xml
*.VisualState.xml
TestResult.xml

# =========================
# Package managers / Node
# =========================
node_modules/
# Paket
.paket/paket.exe
paket-files/

# =========================
# NuGet outputs
# =========================
*.nupkg
*.snupkg
# legacy packages folder if used
packages/

# =========================
# EF / Data
# =========================
# ملاحظة: عادة "مجلد Migrations" يجب تعقّبه في Git
# *.mdf *.ldf (قواعد بيانات محلية)
*.mdf
*.ldf
*.ndf

# =========================
# Logs / Temps / Misc
# =========================
*.log
*.tmp
*.tmp_proj
*.pidb
*.svclog
*.scc
*.e2e
.healthchecksdb
.localhistory/
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
.DS_Store
mono_crash.*

# =========================
# C/C++ and VS internals
# =========================
*_i.c
*_p.c
*_h.h
*.ilk
*.meta
*.obj
*.iobj
*.pch
*.pdb
*.ipdb
*.pgc
*.pgd
*.rsp
*.sbr
*.tlb
*.tli
*.tlh
.builds
_VC.db
*.VC.db
*.VC.VC.opendb
ipch/
*.opendb
*.opensdf
*.sdf
*.cachefile

# =========================
# Web/Publish/Azure
# =========================
publish/
*.[Pp]ublish.xml
*.azurePubxml
*.pubxml
*.publishproj
csx/
ecf/
rcf/
AppPackages/
BundleArtifacts/
Package.StoreAssociation.xml
_pkginfo.txt
*.appx
*.appxbundle
*.appxupload

# Docker (محلي)
docker-compose.override.yml

# =========================
# Style / Analyzers / Tools
# =========================
StyleCopReport.xml
OpenCover/
*.dotCover
.axoCover/*
!.axoCover/settings.json
.fake/
.cr/personal

# =========================
# Old / Rarely used (keep if you need)
# =========================
# Generated files
Generated\ Files/
Generated_Code/
orleans.codegen.cs

# =========================
# Secrets / Env (محلي فقط)
# =========================
.env
.env.*
*.secret
*.secrets
# عادةً لا تعقّب ملفات الإعدادات المحلية
appsettings.*.local.json
appsettings.Local.json

# =========================
# Azure DevOps / Pipelines (اختياري)
# إذا عندك ملفات خاصة محليًا فقط، تجاهل نسخ override
# azure-pipelines.local.yml

# =========================
# Vault specific
# =========================
.vault-token
vault-data/
