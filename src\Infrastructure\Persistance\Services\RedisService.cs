// Update constructor to include audit service
private readonly IRedisAuditService _auditService;

public RedisService(IConnectionMultiplexer connection, ILogger<RedisService> logger, IRedisAuditService auditService)
{
    _connection = connection;
    _logger = logger;
    _auditService = auditService;
    _db = _connection.GetDatabase();
}

// Update methods to include audit logging
public async Task<bool> SetValueAsync(string key, string value, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
{
    try
    {
        var oldValue = await _db.StringGetAsync(key);
        var result = await _db.StringSetAsync(key, value, expiry);
        
        await _auditService.LogOperationAsync("SET", key, oldValue, value, result, cancellationToken: cancellationToken);
        return result;
    }
    catch (Exception ex)
    {
        await _auditService.LogOperationAsync("SET", key, null, value, false, ex.Message, cancellationToken);
        throw;
    }
}

public async Task<bool> DeleteKeyAsync(string key, CancellationToken cancellationToken = default)
{
    try
    {
        var oldValue = await _db.StringGetAsync(key);
        var result = await _db.KeyDeleteAsync(key);
        
        await _auditService.LogOperationAsync("DELETE", key, oldValue, null, result, cancellationToken: cancellationToken);
        return result;
    }
    catch (Exception ex)
    {
        await _auditService.LogOperationAsync("DELETE", key, null, null, false, ex.Message, cancellationToken);
        throw;
    }
}