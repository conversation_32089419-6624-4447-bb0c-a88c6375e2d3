# RedisKeyManager API Overview

This document provides an overview of the Redis key management endpoints exposed by the API.

## Endpoints

### GET `/api/redis/keys?pattern={pattern}`

Returns a paged list of keys matching the provided pattern.

**Sample request:**

```
GET /api/redis/keys?pattern=user:*
```

**Sample response:**

```json
{
  "keys": ["user:1", "user:2"],
  "nextCursor": 0
}
```

**Error codes:** `500` for server errors.

### GET `/api/redis/value/{key}`

Retrieves the value for the specified key.

**Sample request:**

```
GET /api/redis/value/myKey
```

**Sample response:**

```json
"stored-value"
```

**Error codes:** `400` if the key is missing, `404` if the key does not exist, `500` for server errors.

### PUT `/api/redis/value/{key}`

Updates the value of the specified key.

**Sample request:**

```
PUT /api/redis/value/myKey
{
  "value": "new-value"
}
```

**Sample response:** `204 No Content`

**Error codes:** `400` if the key or value is missing, `404` if the key does not exist, `500` for server errors.

### DELETE `/api/redis/value/{key}`

Deletes the specified key from Redis.

**Sample request:**

```
DELETE /api/redis/value/myKey
```

**Sample response:** `204 No Content`

**Error codes:** `400` if the key is missing, `404` if the key does not exist, `500` for server errors.

### GET `/api/redis/hash/{key}`

Returns all fields and values of a hash key.

**Sample request:**

```
GET /api/redis/hash/user:1
```

**Sample response:**

```json
{
  "name": "Alice",
  "email": "<EMAIL>"
}
```

**Error codes:** `400` if the key is missing, `404` if the key does not exist or is not a hash, `500` for server errors.

### GET `/api/redis/hash/{key}/{field}`

Retrieves the value of a specific field in a hash.

**Sample request:**

```
GET /api/redis/hash/user:1/email
```

**Sample response:**

```json
"<EMAIL>"
```

**Error codes:** `400` if key or field is missing, `404` if the key or field does not exist, `500` for server errors.

### POST `/api/redis/hash/{key}/{field}`

Creates a new field in a hash.

**Sample request:**

```
POST /api/redis/hash/user:1/age
{
  "value": "30"
}
```

**Sample response:** `201 Created`

**Error codes:** `400` if key, field, or value is missing, `409` if the field already exists, `500` for server errors.

### PUT `/api/redis/hash/{key}/{field}`

Updates the value of an existing field in a hash.

**Sample request:**

```
PUT /api/redis/hash/user:1/email
{
  "value": "<EMAIL>"
}
```

**Sample response:** `204 No Content`

**Error codes:** `400` if key or field is missing, `404` if the field does not exist, `500` for server errors.

### DELETE `/api/redis/hash/{key}/{field}`

Deletes a field from a hash key.

**Sample request:**

```
DELETE /api/redis/hash/user:1/email
```

**Sample response:** `204 No Content`

**Error codes:** `400` if key or field is missing, `404` if the field is not found, `500` for server errors.

## Configuration

Set the Redis connection string in `appsettings.json` under `"Redis:ConnectionString"`.
