﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32014.148
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{68887AB6-40EA-4A94-A211-774CA12E5C42}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Core", "Core", "{3EFAEAD3-A5FC-484E-B8EE-0C13AA6130F8}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Infrastructure", "Infrastructure", "{F93E961A-04A5-48BC-8688-85341BA42E36}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RedisKeyManager.Application", "src\Core\Application\RedisKeyManager.Application.csproj", "{92262D71-E9EC-4C42-B7E7-6B3AB4D03A49}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RedisKeyManager.Domain", "src\Core\Domain\RedisKeyManager.Domain.csproj", "{6C2FB35F-7A23-4728-AE8B-6657B3BC406B}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RedisKeyManager.Common", "src\Common\RedisKeyManager.Common.csproj", "{8164FAFB-E319-49A3-940C-5D9B029192CA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RedisKeyManager.Persistence", "src\Infrastructure\Persistance\RedisKeyManager.Persistence.csproj", "{D1768A1F-A59B-4906-804B-7A39F576FEA5}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Web", "Web", "{238972C8-A659-488D-9E17-8A1B3E820ECB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RedisKeyManager.ApiFramework", "src\Web\ApiFramework\RedisKeyManager.ApiFramework.csproj", "{4323D8A3-82BB-4C33-8687-AF2E5ADFA191}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RedisKeyManager.Api", "src\Web\Api\RedisKeyManager.Api.csproj", "{*************-4CF7-A21F-5BF08111848E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "test", "test", "{7C72E0CA-895D-4676-BEBA-2ADB297F1164}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RedisKeyManager.CommandHandler.Tests", "test\RedisKeyManager.CommandHandler.Tests\RedisKeyManager.CommandHandler.Tests.csproj", "{6C1E1613-D92F-4A7A-A360-EDFB69C1DD9C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "RedisKeyManager.Api.EndToEndTests", "test\RedisKeyManager.Api.EndToEndTests\RedisKeyManager.Api.EndToEndTests.csproj", "{5C466BC5-7923-4C07-8EF0-393A3E81A231}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{92262D71-E9EC-4C42-B7E7-6B3AB4D03A49}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{92262D71-E9EC-4C42-B7E7-6B3AB4D03A49}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{92262D71-E9EC-4C42-B7E7-6B3AB4D03A49}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{92262D71-E9EC-4C42-B7E7-6B3AB4D03A49}.Release|Any CPU.Build.0 = Release|Any CPU
		{6C2FB35F-7A23-4728-AE8B-6657B3BC406B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6C2FB35F-7A23-4728-AE8B-6657B3BC406B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6C2FB35F-7A23-4728-AE8B-6657B3BC406B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6C2FB35F-7A23-4728-AE8B-6657B3BC406B}.Release|Any CPU.Build.0 = Release|Any CPU
		{8164FAFB-E319-49A3-940C-5D9B029192CA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8164FAFB-E319-49A3-940C-5D9B029192CA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8164FAFB-E319-49A3-940C-5D9B029192CA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8164FAFB-E319-49A3-940C-5D9B029192CA}.Release|Any CPU.Build.0 = Release|Any CPU
		{D1768A1F-A59B-4906-804B-7A39F576FEA5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D1768A1F-A59B-4906-804B-7A39F576FEA5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D1768A1F-A59B-4906-804B-7A39F576FEA5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D1768A1F-A59B-4906-804B-7A39F576FEA5}.Release|Any CPU.Build.0 = Release|Any CPU
		{4323D8A3-82BB-4C33-8687-AF2E5ADFA191}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4323D8A3-82BB-4C33-8687-AF2E5ADFA191}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4323D8A3-82BB-4C33-8687-AF2E5ADFA191}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4323D8A3-82BB-4C33-8687-AF2E5ADFA191}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-4CF7-A21F-5BF08111848E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-4CF7-A21F-5BF08111848E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-4CF7-A21F-5BF08111848E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-4CF7-A21F-5BF08111848E}.Release|Any CPU.Build.0 = Release|Any CPU
		{6C1E1613-D92F-4A7A-A360-EDFB69C1DD9C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6C1E1613-D92F-4A7A-A360-EDFB69C1DD9C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6C1E1613-D92F-4A7A-A360-EDFB69C1DD9C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6C1E1613-D92F-4A7A-A360-EDFB69C1DD9C}.Release|Any CPU.Build.0 = Release|Any CPU
		{5C466BC5-7923-4C07-8EF0-393A3E81A231}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5C466BC5-7923-4C07-8EF0-393A3E81A231}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5C466BC5-7923-4C07-8EF0-393A3E81A231}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5C466BC5-7923-4C07-8EF0-393A3E81A231}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{3EFAEAD3-A5FC-484E-B8EE-0C13AA6130F8} = {68887AB6-40EA-4A94-A211-774CA12E5C42}
		{F93E961A-04A5-48BC-8688-85341BA42E36} = {68887AB6-40EA-4A94-A211-774CA12E5C42}
		{92262D71-E9EC-4C42-B7E7-6B3AB4D03A49} = {3EFAEAD3-A5FC-484E-B8EE-0C13AA6130F8}
		{6C2FB35F-7A23-4728-AE8B-6657B3BC406B} = {3EFAEAD3-A5FC-484E-B8EE-0C13AA6130F8}
		{8164FAFB-E319-49A3-940C-5D9B029192CA} = {68887AB6-40EA-4A94-A211-774CA12E5C42}
		{D1768A1F-A59B-4906-804B-7A39F576FEA5} = {F93E961A-04A5-48BC-8688-85341BA42E36}
		{238972C8-A659-488D-9E17-8A1B3E820ECB} = {68887AB6-40EA-4A94-A211-774CA12E5C42}
		{4323D8A3-82BB-4C33-8687-AF2E5ADFA191} = {238972C8-A659-488D-9E17-8A1B3E820ECB}
		{*************-4CF7-A21F-5BF08111848E} = {238972C8-A659-488D-9E17-8A1B3E820ECB}
		{6C1E1613-D92F-4A7A-A360-EDFB69C1DD9C} = {7C72E0CA-895D-4676-BEBA-2ADB297F1164}
		{5C466BC5-7923-4C07-8EF0-393A3E81A231} = {7C72E0CA-895D-4676-BEBA-2ADB297F1164}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {D550EB2A-3876-4261-981F-44C6D936D367}
	EndGlobalSection
EndGlobal
