# RedisKeyManager

RedisKeyManager is a .NET 8 solution that provides a Web API for managing keys in a Redis instance, including string and hash operations.

## Repository Structure

- `src/RedisKeyManager.Api` – ASP.NET Core Web API project
- `tests/RedisKeyManager.Api.Tests` – xUnit test project
- `samples/RedisHashSample` – console app demonstrating service usage
- `docs/` – additional documentation

## Getting Started

```bash
# Restore packages
 dotnet restore

# Build the solution
 dotnet build

# Run tests
 dotnet test

# Run the API
 dotnet run --project src/RedisKeyManager.Api
```

The API exposes Swagger UI at `/swagger` when running in development.

Configuration for the Redis connection is located in `src/RedisKeyManager.Api/appsettings.json` under `"Redis:ConnectionString"`.

## Samples

A console application demonstrating basic value and hash operations is available:

```bash
dotnet run --project samples/RedisHashSample
```

The sample expects a Redis server available at `localhost:6379`.
