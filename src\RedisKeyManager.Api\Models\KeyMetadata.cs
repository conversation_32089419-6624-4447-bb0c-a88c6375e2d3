using System;

namespace RedisKeyManager.Api.Models
{
    /// <summary>
    /// Represents metadata about a Redis key.
    /// </summary>
    public class KeyMetadata
    {
        /// <summary>
        /// Gets or sets the remaining time to live for the key.
        /// </summary>
        public TimeSpan? Ttl { get; set; }

        /// <summary>
        /// Gets or sets the Redis data type of the key.
        /// </summary>
        public string Type { get; set; } = string.Empty;
    }
}
