using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using RedisKeyManager.Application.Services;
using RedisKeyManager.Domain.Entities.Audit;
using RedisKeyManager.Persistence.Db;

namespace RedisKeyManager.Persistence.Services
{
    public class RedisAuditService : IRedisAuditService
    {
        private readonly AppDbContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;

        public RedisAuditService(AppDbContext context, IHttpContextAccessor httpContextAccessor)
        {
            _context = context;
            _httpContextAccessor = httpContextAccessor;
        }

        public async Task LogOperationAsync(string operation, string key, string? oldValue = null, string? newValue = null, bool success = true, string? errorMessage = null, CancellationToken cancellationToken = default)
        {
            var httpContext = _httpContextAccessor.HttpContext;
            
            var auditLog = new RedisAuditLog
            {
                Operation = operation,
                Key = key,
                OldValue = oldValue,
                NewValue = newValue,
                Success = success,
                ErrorMessage = errorMessage,
                Timestamp = DateTime.UtcNow,
                IpAddress = httpContext?.Connection?.RemoteIpAddress?.ToString(),
                UserAgent = httpContext?.Request?.Headers["User-Agent"].FirstOrDefault(),
                UserName = httpContext?.User?.Identity?.Name
            };

            _context.RedisAuditLogs.Add(auditLog);
            await _context.SaveChangesAsync(cancellationToken);
        }

        public async Task<IEnumerable<RedisAuditLog>> GetAuditLogsAsync(DateTime from, DateTime? to = null, int page = 1, int pageSize = 10, CancellationToken cancellationToken = default)
        {
            var query = _context.RedisAuditLogs
                .Where(x => x.Timestamp >= from);

            if (to.HasValue)
                query = query.Where(x => x.Timestamp <= to.Value);

            return await query
                .OrderByDescending(x => x.Timestamp)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync(cancellationToken);
        }
    }
}