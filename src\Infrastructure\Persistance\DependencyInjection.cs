using System.Reflection;
using RedisKeyManager.Common.General;
using RedisKeyManager.Persistence.Db;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using StackExchange.Redis;
using RedisKeyManager.Application.Redis.Abstractions;
using RedisKeyManager.Persistence.Redis;
using RedisKeyManager.Domain.IRepositories;
using RedisKeyManager.Persistence.Repositories;
using Microsoft.Extensions.Hosting;

namespace RedisKeyManager.Persistence
{
    /// <summary>
    /// امتدادات تسجيل الخدمات الخاصة بطبقة البنية التحتية.
    /// تقوم بتهيئة قواعد البيانات وإعدادات Redis.
    /// </summary>
    public static class DependencyInjection
    {
        /// <summary>
        /// تسجيل خدمات البنية التحتية داخل حاوية الاعتمادية.
        /// </summary>
        public static IServiceCollection AddPersistance(this IServiceCollection services, IConfiguration configuration)
        {
            var appOptions = configuration.GetSection(nameof(AppOptions)).Get<AppOptions>();

            // تسجيل جميع معالجات MediatR الموجودة في هذه الطبقة
            services.AddMediatR(cfg => cfg.RegisterServicesFromAssembly(Assembly.GetExecutingAssembly()));

            // تسجيل المستودعات (Repositories)
            services.AddScoped(typeof(IRepository<>), typeof(Repository<>));
            services.AddScoped(typeof(IReanOnlyRepository<>), typeof(EfReadOnlyRepository<>));
            services.AddScoped<IAuditRepository, AuditRepository>();
            services.AddScoped<IRefreshTokenRepository, RefreshTokenRepository>();
            services.AddScoped<IUserRepository, UserRepository>();

            // تسجيل سياقات القراءة والكتابة باستخدام سلاسل الاتصال المخصصة
            services.AddScoped((serviceProvider) =>
            {
                var option = CreateContextOptions(appOptions.ReadDatabaseConnectionString);
                return new CleanArchReadOnlyDbContext(option);
            });

            services.AddScoped((serviceProvider) =>
            {
                var option = CreateContextOptions(appOptions.WriteDatabaseConnectionString);
                return new CleanArchWriteDbContext(option);
            });

            // وظيفة محلية لإنشاء خيارات DbContext من سلسلة اتصال
            DbContextOptions<AppDbContext> CreateContextOptions(string connectionString)
            {
                var contextOptions = new DbContextOptionsBuilder<AppDbContext>()
                                     .UseSqlServer(connectionString)
                                     .Options;

                return contextOptions;
            }

            services.AddDbContext<AppDbContext>(options => options.UseSqlServer(appOptions.WriteDatabaseConnectionString));

            services.AddScoped<IAppDbContext, AppDbContext>();

            // تهيئة اتصال Redis وإضافة الخدمات ذات الصلة إذا كانت سلسلة الاتصال متوفرة
            var redisConnection = configuration.GetSection("Redis").GetValue<string>("ConnectionString");
            if (!string.IsNullOrEmpty(redisConnection))
            {
                services.AddSingleton<IConnectionMultiplexer>(sp => ConnectionMultiplexer.Connect(redisConnection));
                services.AddScoped<IRedisClient, RedisClient>();
                services.AddSingleton<RedisAuditQueue>();
                services.AddScoped<IRedisAuditWriter, RedisAuditWriter>();
                services.AddHostedService<RedisAuditLogProcessor>();
                services.AddScoped<IRedisMonitoringService, RedisMonitoringService>();
                services.AddHttpContextAccessor();
            }

            services.AddScoped<IRedisAuditService, RedisAuditService>();

            return services;
        }
    }
}
