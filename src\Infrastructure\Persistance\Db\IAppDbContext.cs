﻿using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace RedisKeyManager.Persistence.Db
{
    public interface IAppDbContext
    {
        DbSet<T> Set<T>() where T : class;
        Task<int> SaveChangesAsync(CancellationToken cancellation);
        Task<int> ExecuteSqlRawAsync(string query, CancellationToken cancellationToken);
        Task<int> ExecuteSqlRawAsync(string query);
    }
}
