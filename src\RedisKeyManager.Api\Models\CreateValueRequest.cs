using System;
using System.ComponentModel.DataAnnotations;

namespace RedisKeyManager.Api.Models
{
    /// <summary>
    /// Represents a request to create a new Redis key with a value.
    /// </summary>
    public class CreateValueRequest
    {
        /// <summary>
        /// The value to set.
        /// </summary>
        [Required]
        public string? Value { get; set; }

        /// <summary>
        /// Optional expiration for the key.
        /// </summary>
        public TimeSpan? Expiry { get; set; }
    }
}
