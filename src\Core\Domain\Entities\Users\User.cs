﻿using Microsoft.AspNetCore.Identity;
using RedisKeyManager.Domain.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace RedisKeyManager.Domain.Entities.Users
{
    public class User : IdentityUser<int>, IEntity<int>
    {
        public User()
        {
            IsActive = true;
        }

        public string FullName { get; set; }

        public int Age { get; set; }

        public GenderType Gender { get; set; }

        public bool IsActive { get; set; }

        public DateTimeOffset? LastLoginDate { get; set; }
    }

    public enum GenderType
    {
        [Display(Name = "مرد")]
        Male = 1,

        [Display(Name = "زن")]
        Female = 2
    }
}
