using System;
using System.ComponentModel.DataAnnotations;

namespace RedisKeyManager.Api.Models
{
    /// <summary>
    /// Represents a request to update the value of a Redis key.
    /// </summary>
    public class UpdateValueRequest
    {
        /// <summary>
        /// The new value to set.
        /// </summary>
        [Required]
        public string? Value { get; set; }

        /// <summary>
        /// Optional expiration for the key.
        /// </summary>
        public TimeSpan? Expiry { get; set; }
    }
}
