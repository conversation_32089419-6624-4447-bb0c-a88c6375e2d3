﻿using MediatR;
using RedisKeyManager.Application.Products.Query.GetProducts;
using RedisKeyManager.Common.Utilities;
using RedisKeyManager.Domain.Entities.Products;
using RedisKeyManager.Persistence.Db;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace RedisKeyManager.Persistence.QueryHandlers.Products
{
    public class GetProductsQueryHandler : IRequestHandler<GetProductsQuery, PagedResult<Product>>
    {
        private readonly CleanArchReadOnlyDbContext _dbContext;

        public GetProductsQueryHandler(CleanArchReadOnlyDbContext dbContext)
        {
            _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        }

        public async Task<PagedResult<Product>> Handle(GetProductsQuery request, CancellationToken cancellationToken)
        {
            var products = await _dbContext.Set<Product>().GetPaged(request.Page, request.PageSize);

            return products;
        }
    }
}
