{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "AppOptions": {"DatabaseConnectionString": "Server=.;Database=Db;User Id=username;Password=password;", "RedisConnectionString": "localhost:6379", "AuthenticationServerUri": "", "ActivateSwagger": true, "CorsEnableUris": []}, "SiteSettings": {"JwtSettings": {"SecretKey": "<PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON>", "EncryptKey": "16CharEncryptKey", "Issuer": "CleanArchTemplate", "Audience": "CleanArchTemplate", "NotBeforeMinutes": "0", "ExpirationMinutes": "1440", "RefreshTokenValidityInDays": 7}, "IdentitySettings": {"PasswordRequireDigit": "true", "PasswordRequiredLength": "6", "PasswordRequireNonAlphanumeric": "false", "PasswordRequireUppercase": "false", "PasswordRequireLowercase": "false", "RequireUniqueEmail": "true"}}, "Serilog": {"WriteTo": [{"Name": "Elasticsearch", "Args": {"nodeUris": "localhost:9200", "indexFormat": "cleanArch-log", "restrictedToMinimumLevel": "Warning", "autoRegisterTemplate": true, "autoRegisterTemplateVersion": "ESv6"}}, {"Name": "<PERSON><PERSON><PERSON>", "Args": {"restrictedToMinimumLevel": "Information"}}]}}