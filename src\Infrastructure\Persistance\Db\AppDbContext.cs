using RedisKeyManager.Common.Utilities;
using RedisKeyManager.Domain.Entities;
using RedisKeyManager.Domain.Entities.Users;
using RedisKeyManager.Domain.Entities.Redis;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using System.Threading;
using System.Threading.Tasks;

namespace RedisKeyManager.Persistence.Db
{
    public class AppDbContext : IdentityDbContext<User, Role, int>, IAppDbContext
    {
        public AppDbContext(DbContextOptions<AppDbContext> options) : base(options)
        {

        }

        public AppDbContext() { }

        public DbSet<RedisAuditLog> RedisAuditLogs { get; set; }
        public DbSet<AuditLog> AuditLogs { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.ApplyConfiguration(new RedisAuditLogConfiguration());
            
            base.OnModelCreating(modelBuilder);

            var entitiesAssembly = typeof(IEntity).Assembly;

            modelBuilder.RegisterAllEntities<IEntity>(entitiesAssembly);
            modelBuilder.ApplyConfigurationsFromAssembly(typeof(IEntity).Assembly);
            modelBuilder.AddPluralizingTableNameConvention();

            modelBuilder.Entity<RedisAuditLog>(b =>
            {
                b.HasIndex(x => x.TimestampUtc);
                b.HasIndex(x => x.Key);
                b.HasIndex(x => x.HashField).HasFilter("[HashField] IS NOT NULL");
            });

            modelBuilder.Entity<AuditLog>(b =>
            {
                b.HasIndex(x => x.Timestamp);
                b.HasIndex(x => x.UserId);
            });
        }

        public async Task<int> ExecuteSqlRawAsync(string query, CancellationToken cancellationToken)
        {
            var result = await base.Database.ExecuteSqlRawAsync(query, cancellationToken);
            return result;
        }

        public async Task<int> ExecuteSqlRawAsync(string query) => await ExecuteSqlRawAsync(query, CancellationToken.None);
    }
}
