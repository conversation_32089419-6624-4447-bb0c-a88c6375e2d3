﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.7" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.7" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.7">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7" />
	  <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.7">
	    <PrivateAssets>all</PrivateAssets>
	    <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
	  </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.FileExtensions" Version="8.0.1" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="PolyCache" Version="1.2.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Common\RedisKeyManager.Common.csproj" />
    <ProjectReference Include="..\..\Core\Application\RedisKeyManager.Application.csproj" />
    <ProjectReference Include="..\..\Core\Domain\RedisKeyManager.Domain.csproj" />
  </ItemGroup>

</Project>
