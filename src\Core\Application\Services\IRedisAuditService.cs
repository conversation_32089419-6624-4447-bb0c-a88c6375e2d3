using RedisKeyManager.Domain.Entities.Audit;

namespace RedisKeyManager.Application.Services
{
    public interface IRedisAuditService
    {
        Task LogOperationAsync(string operation, string key, string? oldValue = null, string? newValue = null, bool success = true, string? errorMessage = null, CancellationToken cancellationToken = default);
        Task<IEnumerable<RedisAuditLog>> GetAuditLogsAsync(DateTime from, DateTime? to = null, int page = 1, int pageSize = 10, CancellationToken cancellationToken = default);
    }
}