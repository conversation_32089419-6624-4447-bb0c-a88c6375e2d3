using Microsoft.Extensions.Logging;
using Moq;
using RedisKeyManager.Common.Exceptions;
using RedisKeyManager.Persistence.CommandHandlers.Products;
using RedisKeyManager.Persistence.Db;
using System.Threading;
using System.Threading.Tasks;
using Xunit;

namespace RedisKeyManager.CommandHandler.Tests
{
    public class AddProductCommandHandlerTests
    {
        [Fact]
        public async Task Should_ThrowException_When_InputIsNull()
        {
            var dbContext = new Mock<CleanArchWriteDbContext>();
            var logger = new Mock<ILogger<AddProductCommandHandler>>();

            var commandHandler = new AddProductCommandHandler(dbContext.Object, logger.Object);

            await Assert.ThrowsAsync<InvalidNullInputException>(() => commandHandler.Handle(null, CancellationToken.None));
        }
    }
}