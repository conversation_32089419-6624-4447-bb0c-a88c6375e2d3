using System.Collections.Generic;

namespace RedisKeyManager.Api.Models
{
    /// <summary>
    /// Represents a paged result from scanning Redis keys.
    /// </summary>
    public class KeySearchResult
    {
        /// <summary>
        /// Gets or sets the keys returned for the current page.
        /// </summary>
        public IEnumerable<string> Keys { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the cursor to use for the next scan.
        /// </summary>
        public long NextCursor { get; set; }
    }
}
