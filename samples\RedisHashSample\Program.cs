using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using RedisKeyManager.Api.Services;

namespace RedisHashSample;

internal class Program
{
    private static async Task Main()
    {
        using var loggerFactory = LoggerFactory.Create(builder => builder.AddSimpleConsole().SetMinimumLevel(LogLevel.Information));
        var logger = loggerFactory.CreateLogger<RedisService>();
        using var connection = await ConnectionMultiplexer.ConnectAsync("localhost");
        var service = new RedisService(connection, logger);

        const string stringKey = "sample:string";
        const string hashKey = "sample:hash";

        await service.SetValueAsync(stringKey, "hello world");
        var stringValue = await service.GetValueAsync(stringKey);
        Console.WriteLine($"{stringKey} = {stringValue}");

        await service.SetHashFieldAsync(hashKey, "name", "Alice");
        await service.SetHashFieldAsync(hashKey, "email", "<EMAIL>");

        var hash = await service.GetHashAsync(hashKey);
        Console.WriteLine("Initial hash contents:");
        foreach (var kv in hash!)
        {
            Console.WriteLine($"  {kv.Key}: {kv.Value}");
        }

        await service.SetHashFieldAsync(hashKey, "email", "<EMAIL>");
        var email = await service.GetHashFieldAsync(hashKey, "email");
        Console.WriteLine($"Updated email: {email}");

        await service.DeleteHashFieldAsync(hashKey, "name");
        hash = await service.GetHashAsync(hashKey);
        Console.WriteLine("Final hash contents:");
        foreach (var kv in hash!)
        {
            Console.WriteLine($"  {kv.Key}: {kv.Value}");
        }

        await service.DeleteKeyAsync(stringKey);
        await service.DeleteKeyAsync(hashKey);
    }
}
