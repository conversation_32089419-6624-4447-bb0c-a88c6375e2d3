using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using RedisKeyManager.Api.Models;

namespace RedisKeyManager.Api.Services
{
    /// <summary>
    /// Provides methods for interacting with Redis.
    /// </summary>
    public interface IRedisService
    {
        /// <summary>
        /// Searches for keys that match the specified pattern using a cursor-based scan.
        /// </summary>
        /// <param name="pattern">The pattern to search for. Wildcards are added automatically.</param>
        /// <param name="cursor">The cursor from which to start the scan. Set to <c>0</c> to start from the beginning.</param>
        /// <param name="pageSize">The maximum number of keys to return.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>A <see cref="KeySearchResult" /> containing the keys and cursor for the next scan.</returns>
        Task<KeySearchResult> SearchKeysAsync(string pattern, long cursor = 0, int pageSize = 10, CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves the value for the specified key.
        /// </summary>
        /// <param name="key">The Redis key.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>The value, or <c>null</c> if the key does not exist.</returns>
        Task<string?> GetValueAsync(string key, CancellationToken cancellationToken = default);

        /// <summary>
        /// Sets the value for the specified key.
        /// </summary>
        /// <param name="key">The Redis key.</param>
        /// <param name="value">The value to set.</param>
        /// <param name="expiry">Optional expiration for the key.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns><c>true</c> if the value was set; otherwise <c>false</c>.</returns>
        Task<bool> SetValueAsync(string key, string value, TimeSpan? expiry = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Sets the value for the specified key only if it does not exist.
        /// </summary>
        /// <param name="key">The Redis key.</param>
        /// <param name="value">The value to set.</param>
        /// <param name="expiry">Optional expiration for the key.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns><c>true</c> if the value was set; otherwise <c>false</c>.</returns>
        Task<bool> SetValueIfNotExistsAsync(string key, string value, TimeSpan? expiry = null, CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves all fields and values of a hash key.
        /// </summary>
        /// <param name="key">The Redis hash key.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>A dictionary of field-value pairs or <c>null</c> if the key does not exist or is not a hash.</returns>
        Task<Dictionary<string, string>?> GetHashAsync(string key, CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves the value of a specific field in a hash.
        /// </summary>
        /// <param name="key">The Redis hash key.</param>
        /// <param name="field">The field within the hash.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>The field value or <c>null</c> if the field or key does not exist.</returns>
        Task<string?> GetHashFieldAsync(string key, string field, CancellationToken cancellationToken = default);

        /// <summary>
        /// Sets the value of a field in a hash.
        /// </summary>
        /// <param name="key">The Redis hash key.</param>
        /// <param name="field">The field to set.</param>
        /// <param name="value">The value to assign.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns><c>true</c> if the field was newly added; otherwise <c>false</c>.</returns>
        Task<bool> SetHashFieldAsync(string key, string field, string value, CancellationToken cancellationToken = default);

        /// <summary>
        /// Sets the value of a field in a hash only if it does not already exist.
        /// </summary>
        /// <param name="key">The Redis hash key.</param>
        /// <param name="field">The field to set.</param>
        /// <param name="value">The value to assign.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns><c>true</c> if the field was added; otherwise <c>false</c>.</returns>
        Task<bool> SetHashFieldIfNotExistsAsync(string key, string field, string value, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes a field from a hash key.
        /// </summary>
        /// <param name="key">The Redis hash key.</param>
        /// <param name="field">The field to delete.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns><c>true</c> if the field was deleted; otherwise <c>false</c>.</returns>
        Task<bool> DeleteHashFieldAsync(string key, string field, CancellationToken cancellationToken = default);

        /// <summary>
        /// Deletes the specified key.
        /// </summary>
        /// <param name="key">The Redis key.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns><c>true</c> if the key was deleted; otherwise <c>false</c>.</returns>
        Task<bool> DeleteKeyAsync(string key, CancellationToken cancellationToken = default);

        /// <summary>
        /// Retrieves metadata about a key, such as TTL and type.
        /// </summary>
        /// <param name="key">The Redis key.</param>
        /// <param name="cancellationToken">Token to observe while waiting for the task to complete.</param>
        /// <returns>The key metadata or <c>null</c> if the key does not exist.</returns>
        Task<KeyMetadata?> GetMetadataAsync(string key, CancellationToken cancellationToken = default);
    }
}
