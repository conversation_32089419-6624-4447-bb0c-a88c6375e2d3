using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using RedisKeyManager.Api.Models;

namespace RedisKeyManager.Api.Services
{
    /// <summary>
    /// Implementation of <see cref="IRedisService" /> using StackExchange.Redis.
    /// </summary>
    public class RedisService : IRedisService
    {
        private readonly IConnectionMultiplexer _connection;
        private readonly ILogger<RedisService> _logger;
        private readonly IDatabase _db;

        /// <summary>
        /// Initializes a new instance of the <see cref="RedisService" /> class.
        /// </summary>
        /// <param name="connection">The Redis connection multiplexer.</param>
        /// <param name="logger">The logger instance.</param>
        public RedisService(IConnectionMultiplexer connection, ILogger<RedisService> logger)
        {
            _connection = connection;
            _logger = logger;
            _db = _connection.GetDatabase();
        }

        /// <inheritdoc />
        public async Task<KeySearchResult> SearchKeysAsync(string pattern, long cursor = 0, int pageSize = 10, CancellationToken cancellationToken = default)
        {
            pattern ??= string.Empty;
            var server = GetServer();

            var result = await server
                .ExecuteAsync("SCAN", cursor.ToString(), "MATCH", $"*{pattern}*", "COUNT", pageSize.ToString())
                .WaitAsync(cancellationToken);

            var inner = (RedisResult[])result!;
            var nextCursor = (long)inner[0];
            var keys = ((RedisResult[])inner[1]!).Select(x => (string)x!);

            return new KeySearchResult
            {
                Keys = keys,
                NextCursor = nextCursor
            };
        }

        /// <inheritdoc />
        public async Task<string?> GetValueAsync(string key, CancellationToken cancellationToken = default)
        {
            var value = await _db.StringGetAsync(key).WaitAsync(cancellationToken);
            return value.IsNull ? null : value.ToString();
        }

        /// <inheritdoc />
        public Task<bool> SetValueAsync(string key, string value, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
        {
            return _db.StringSetAsync(key, value, expiry, keepTtl: false, when: When.Always).WaitAsync(cancellationToken);
        }

        /// <inheritdoc />
        public Task<bool> SetValueIfNotExistsAsync(string key, string value, TimeSpan? expiry = null, CancellationToken cancellationToken = default)
        {
            return _db.StringSetAsync(key, value, expiry, keepTtl: false, when: When.NotExists).WaitAsync(cancellationToken);
        }

        /// <inheritdoc />
        public async Task<Dictionary<string, string>?> GetHashAsync(string key, CancellationToken cancellationToken = default)
        {
            var type = await _db.KeyTypeAsync(key).WaitAsync(cancellationToken);
            if (type != RedisType.Hash)
            {
                return null;
            }

            var entries = await _db.HashGetAllAsync(key).WaitAsync(cancellationToken);
            return entries.ToDictionary(e => e.Name.ToString(), e => e.Value.ToString());
        }

        /// <inheritdoc />
        public async Task<string?> GetHashFieldAsync(string key, string field, CancellationToken cancellationToken = default)
        {
            var value = await _db.HashGetAsync(key, field).WaitAsync(cancellationToken);
            return value.IsNull ? null : value.ToString();
        }

        /// <inheritdoc />
        public Task<bool> SetHashFieldAsync(string key, string field, string value, CancellationToken cancellationToken = default)
        {
            return _db.HashSetAsync(key, field, value, When.Always).WaitAsync(cancellationToken);
        }

        /// <inheritdoc />
        public Task<bool> SetHashFieldIfNotExistsAsync(string key, string field, string value, CancellationToken cancellationToken = default)
        {
            return _db.HashSetAsync(key, field, value, When.NotExists).WaitAsync(cancellationToken);
        }

        /// <inheritdoc />
        public Task<bool> DeleteHashFieldAsync(string key, string field, CancellationToken cancellationToken = default)
        {
            return _db.HashDeleteAsync(key, field).WaitAsync(cancellationToken);
        }

        /// <inheritdoc />
        public Task<bool> DeleteKeyAsync(string key, CancellationToken cancellationToken = default)
        {
            return _db.KeyDeleteAsync(key).WaitAsync(cancellationToken);
        }

        /// <inheritdoc />
        public async Task<KeyMetadata?> GetMetadataAsync(string key, CancellationToken cancellationToken = default)
        {
            var type = await _db.KeyTypeAsync(key).WaitAsync(cancellationToken);
            if (type == RedisType.None)
            {
                return null;
            }

            var ttl = await _db.KeyTimeToLiveAsync(key).WaitAsync(cancellationToken);
            return new KeyMetadata
            {
                Ttl = ttl,
                Type = type.ToString()
            };
        }

        private IServer GetServer()
        {
            var endpoint = _connection.GetEndPoints().First();
            return _connection.GetServer(endpoint);
        }
    }
}
