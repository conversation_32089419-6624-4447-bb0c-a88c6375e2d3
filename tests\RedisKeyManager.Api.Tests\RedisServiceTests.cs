using System;
using System.Linq;
using System.Net;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using StackExchange.Redis;
using RedisKeyManager.Api.Models;
using RedisKeyManager.Api.Services;
using Xunit;

namespace RedisKeyManager.Api.Tests;

public class RedisServiceTests
{
    private static RedisService CreateService(
        Mock<IDatabase> dbMock,
        out Mock<IConnectionMultiplexer> connectionMock,
        Mock<IServer>? serverMock = null)
    {
        connectionMock = new Mock<IConnectionMultiplexer>();
        connectionMock.Setup(c => c.GetDatabase(It.IsAny<int>(), It.IsAny<object?>()))
            .Returns(dbMock.Object);

        if (serverMock != null)
        {
            var endpoint = new DnsEndPoint("localhost", 6379);
            connectionMock.Setup(c => c.GetEndPoints(It.IsAny<bool>()))
                .Returns(new EndPoint[] { endpoint });
            connectionMock.Setup(c => c.GetServer(endpoint, It.IsAny<object?>()))
                .Returns(serverMock.Object);
        }

        var logger = Mock.Of<ILogger<RedisService>>();
        return new RedisService(connectionMock.Object, logger);
    }

    [Fact]
    public async Task GetValueAsync_ReturnsNull_WhenKeyMissing()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.StringGetAsync("missing", It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        var service = CreateService(dbMock, out _);

        var result = await service.GetValueAsync("missing");

        Assert.Null(result);
    }

    [Fact]
    public async Task GetValueAsync_ReturnsValue_WhenKeyExists()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.StringGetAsync("key", It.IsAny<CommandFlags>()))
            .ReturnsAsync((RedisValue)"value");

        var service = CreateService(dbMock, out _);

        var result = await service.GetValueAsync("key");

        Assert.Equal("value", result);
    }

    [Fact]
    public async Task SetValueAsync_DelegatesToDatabase()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.StringSetAsync(
                It.IsAny<RedisKey>(),
                It.IsAny<RedisValue>(),
                It.IsAny<TimeSpan?>(),
                It.IsAny<bool>(),
                It.IsAny<When>(),
                It.IsAny<CommandFlags>()))
            .ReturnsAsync(true)
            .Verifiable();

        var service = CreateService(dbMock, out _);

        var result = await service.SetValueAsync("key", "value");

        Assert.True(result);
        dbMock.Verify();
    }

    [Fact]
    public async Task SetValueAsync_PassesExpiry()
    {
        var dbMock = new Mock<IDatabase>();
        var expiry = TimeSpan.FromMinutes(5);
        dbMock.Setup(db => db.StringSetAsync(
                "key",
                "value",
                expiry,
                false,
                When.Always,
                It.IsAny<CommandFlags>()))
            .ReturnsAsync(true)
            .Verifiable();

        var service = CreateService(dbMock, out _);

        await service.SetValueAsync("key", "value", expiry);

        dbMock.Verify();
    }

    [Fact]
    public async Task SetValueIfNotExistsAsync_UsesNotExistsCondition()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.StringSetAsync(
                "key",
                "value",
                null,
                false,
                When.NotExists,
                It.IsAny<CommandFlags>()))
            .ReturnsAsync(true)
            .Verifiable();

        var service = CreateService(dbMock, out _);

        var result = await service.SetValueIfNotExistsAsync("key", "value");

        Assert.True(result);
        dbMock.Verify();
    }

    [Fact]
    public async Task GetMetadataAsync_ReturnsNull_WhenKeyMissing()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.KeyTypeAsync("missing", It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisType.None);

        var service = CreateService(dbMock, out _);

        var result = await service.GetMetadataAsync("missing");

        Assert.Null(result);
    }

    [Fact]
    public async Task GetMetadataAsync_ReturnsMetadata_WhenKeyExists()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.KeyTypeAsync("key", It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisType.String);
        dbMock.Setup(db => db.KeyTimeToLiveAsync("key", It.IsAny<CommandFlags>()))
            .ReturnsAsync(TimeSpan.FromSeconds(10));

        var service = CreateService(dbMock, out _);

        var result = await service.GetMetadataAsync("key");

        Assert.NotNull(result);
        Assert.Equal(TimeSpan.FromSeconds(10), result!.Ttl);
        Assert.Equal("String", result.Type);
    }

    [Fact]
    public async Task GetValueAsync_HonorsCancellation()
    {
        var dbMock = new Mock<IDatabase>();
        var tcs = new TaskCompletionSource<RedisValue>();
        dbMock.Setup(db => db.StringGetAsync("key", It.IsAny<CommandFlags>()))
            .Returns(tcs.Task);

        var service = CreateService(dbMock, out _);
        using var cts = new CancellationTokenSource();

        var task = service.GetValueAsync("key", cts.Token);
        cts.Cancel();

        await Assert.ThrowsAsync<TaskCanceledException>(() => task);
    }

    [Fact]
    public async Task DeleteKeyAsync_DelegatesToDatabase()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.KeyDeleteAsync("key", It.IsAny<CommandFlags>()))
            .ReturnsAsync(true)
            .Verifiable();

        var service = CreateService(dbMock, out _);

        var result = await service.DeleteKeyAsync("key");

        Assert.True(result);
        dbMock.Verify();
    }

    [Fact]
    public async Task SearchKeysAsync_ReturnsKeysAndCursor()
    {
        var dbMock = new Mock<IDatabase>();
        var serverMock = new Mock<IServer>();

        var keyResults = new RedisResult[]
        {
            RedisResult.Create((RedisValue)"key1"),
            RedisResult.Create((RedisValue)"key2")
        };
        var innerArray = new RedisResult[]
        {
            RedisResult.Create((RedisValue)0, ResultType.Integer),
            RedisResult.Create(keyResults)
        };
        var redisResult = RedisResult.Create(innerArray);

        serverMock.Setup(s => s.ExecuteAsync(
                "SCAN",
                It.IsAny<object[]>()))
            .ReturnsAsync(redisResult);

        var service = CreateService(dbMock, out _, serverMock);

        var result = await service.SearchKeysAsync("key");

        Assert.Equal(0, result.NextCursor);
        Assert.True(result.Keys.SequenceEqual(new[] { "key1", "key2" }));
        serverMock.Verify(s => s.ExecuteAsync(
            "SCAN",
            It.Is<object[]>(args =>
                args[0].ToString() == "0" &&
                args[1].ToString() == "MATCH" &&
                args[2].ToString() == "*key*")), Times.Once);
    }

    [Fact]
    public async Task GetHashFieldAsync_ReturnsNull_WhenMissing()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.HashGetAsync("key", "field", It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisValue.Null);

        var service = CreateService(dbMock, out _);

        var result = await service.GetHashFieldAsync("key", "field");

        Assert.Null(result);
    }

    [Fact]
    public async Task GetHashFieldAsync_ReturnsValue_WhenExists()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.HashGetAsync("key", "field", It.IsAny<CommandFlags>()))
            .ReturnsAsync((RedisValue)"value");

        var service = CreateService(dbMock, out _);

        var result = await service.GetHashFieldAsync("key", "field");

        Assert.Equal("value", result);
    }

    [Fact]
    public async Task SetHashFieldIfNotExistsAsync_UsesNotExistsCondition()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.HashSetAsync("key", "field", "value", When.NotExists, It.IsAny<CommandFlags>()))
            .ReturnsAsync(true)
            .Verifiable();

        var service = CreateService(dbMock, out _);

        var result = await service.SetHashFieldIfNotExistsAsync("key", "field", "value");

        Assert.True(result);
        dbMock.Verify();
    }

    [Fact]
    public async Task DeleteHashFieldAsync_DelegatesToDatabase()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.HashDeleteAsync("key", "field", It.IsAny<CommandFlags>()))
            .ReturnsAsync(true)
            .Verifiable();

        var service = CreateService(dbMock, out _);

        var result = await service.DeleteHashFieldAsync("key", "field");

        Assert.True(result);
        dbMock.Verify();
    }

    [Fact]
    public async Task GetHashAsync_ReturnsDictionary_WhenExists()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.KeyTypeAsync("key", It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisType.Hash);
        dbMock.Setup(db => db.HashGetAllAsync("key", It.IsAny<CommandFlags>()))
            .ReturnsAsync(new[] { new HashEntry("f1", "v1"), new HashEntry("f2", "v2") });

        var service = CreateService(dbMock, out _);

        var result = await service.GetHashAsync("key");

        Assert.NotNull(result);
        Assert.Equal("v1", result!["f1"]);
        Assert.Equal("v2", result["f2"]);
    }

    [Fact]
    public async Task GetHashAsync_ReturnsNull_WhenNotHash()
    {
        var dbMock = new Mock<IDatabase>();
        dbMock.Setup(db => db.KeyTypeAsync("key", It.IsAny<CommandFlags>()))
            .ReturnsAsync(RedisType.String);

        var service = CreateService(dbMock, out _);

        var result = await service.GetHashAsync("key");

        Assert.Null(result);
    }
}
